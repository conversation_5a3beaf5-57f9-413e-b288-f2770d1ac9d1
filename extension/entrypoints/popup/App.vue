<template>
  <div class="app">
    <n-config-provider :theme="theme">
      <n-message-provider>
        <div class="container">
          <header class="header">
            <h1 class="title">
              <n-icon size="24" class="icon">
                <svg viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,17A2,2 0 0,0 14,15C14,13.89 13.1,13 12,13A2,2 0 0,0 10,15A2,2 0 0,0 12,17M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6A2,2 0 0,1 4,20V10C4,8.89 4.9,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z" />
                </svg>
              </n-icon>
              SecureFox
            </h1>
            <n-button 
              quaternary 
              circle 
              size="small"
              @click="toggleTheme"
            >
              <n-icon size="16">
                <svg v-if="isDark" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,18C11.11,18 10.26,17.8 9.5,17.45C11.56,16.5 13,14.42 13,12C13,9.58 11.56,7.5 9.5,6.55C10.26,6.2 11.11,6 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />
                </svg>
                <svg v-else viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12,8A4,4 0 0,0 8,12A4,4 0 0,0 12,16A4,4 0 0,0 16,12A4,4 0 0,0 12,8M12,18A6,6 0 0,1 6,12A6,6 0 0,1 12,6A6,6 0 0,1 18,12A6,6 0 0,1 12,18M20,8.69V4H15.31L12,0.69L8.69,4H4V8.69L0.69,12L4,15.31V20H8.69L12,23.31L15.31,20H20V15.31L23.31,12L20,8.69Z" />
                </svg>
              </n-icon>
            </n-button>
          </header>
          
          <main class="main">
            <n-card>
              <div class="welcome">
                <n-icon size="48" color="#18a058">
                  <svg viewBox="0 0 24 24">
                    <path fill="currentColor" d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9M10,16V19.08L13.08,16H20V4H4V16H10Z" />
                  </svg>
                </n-icon>
                <h2>欢迎使用 SecureFox</h2>
                <p>面向开发者的安全密钥管理工具</p>
                <n-space vertical>
                  <n-button type="primary" size="large">
                    开始使用
                  </n-button>
                  <n-button quaternary>
                    了解更多
                  </n-button>
                </n-space>
              </div>
            </n-card>
          </main>
        </div>
      </n-message-provider>
    </n-config-provider>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  NConfigProvider, 
  NMessageProvider, 
  NCard, 
  NButton, 
  NIcon, 
  NSpace,
  darkTheme 
} from 'naive-ui';

const isDark = ref(false);
const theme = computed(() => isDark.value ? darkTheme : null);

const toggleTheme = () => {
  isDark.value = !isDark.value;
};
</script>

<style scoped>
.app {
  width: 380px;
  height: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.icon {
  color: #18a058;
}

.main {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.welcome {
  text-align: center;
  padding: 32px 16px;
}

.welcome h2 {
  margin: 16px 0 8px;
  font-size: 20px;
  font-weight: 600;
}

.welcome p {
  margin: 0 0 24px;
  color: #666;
  font-size: 14px;
}
</style>
