// Content Script - 将在后续任务中实现自动填充功能
console.log('SecureFox content script loaded');

// 基础的消息监听器
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Content script received message:', message);
  
  switch (message.type) {
    case 'PING':
      sendResponse({ success: true, message: 'Content script is active' });
      break;
    default:
      sendResponse({ success: false, message: 'Unknown message type' });
  }
  
  return true; // 保持消息通道开放
});

export {};
