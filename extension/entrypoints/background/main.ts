// Background Script - 后台服务
console.log('SecureFox background script loaded');

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener((details) => {
  console.log('SecureFox extension installed:', details.reason);
  
  if (details.reason === 'install') {
    // 首次安装时的初始化逻辑
    console.log('First time installation');
  }
});

// 处理来自popup和content script的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);
  
  switch (message.type) {
    case 'GET_ACTIVE_TAB':
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        sendResponse({ success: true, tab: tabs[0] });
      });
      return true; // 异步响应
      
    default:
      sendResponse({ success: false, message: 'Unknown message type' });
  }
});

export {};
